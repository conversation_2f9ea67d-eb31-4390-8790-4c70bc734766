<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Lees het volledige artikel op SuppRadar - <PERSON><PERSON><PERSON><PERSON>ijk onderbouwde informatie over supplementen, voeding en training.">
    <meta name="keywords" content="supplement artikel, fitness blog, voeding tips, training advies">
    <meta name="author" content="SuppRadar">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="article">
    <meta property="og:url" content="">
    <meta property="og:title" content="">
    <meta property="og:description" content="">
    <meta property="og:image" content="">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="">
    <meta property="twitter:title" content="">
    <meta property="twitter:description" content="">
    <meta property="twitter:image" content="">

    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="main.css">
    <title>Blog Artikel - SuppRadar</title>
</head>
<body class="blog-page">
    <div class="header">
        <!-- Mobile hamburger menu -->
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="logo-container">
            <a href="./index.html" class="logo-link">
                <img src="./Afbeeldingen/logo.png" alt="SuppRadar Logo">
                <h1>SuppRadar</h1>
            </a>
        </div>
        <div class="nav-menu" id="nav-menu">
            <a href="./index.html">Home</a>
            <a href="./supplementen.html">Supplementen</a>
            <a href="./calorie-calculator.html">Calorie Calculator</a>
            <div class="dropdown">
                <a href="./shop-per-doel.html">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="./shop-per-doel.html">Gewichtsverlies</a>
                    <a href="./shop-per-doel.html">Spieropbouw</a>
                    <a href="./shop-per-doel.html">Uithoudingsvermogen</a>
                    <a href="./shop-per-doel.html">Fitness & Onderhoud</a>
                    <a href="./shop-per-doel.html">Gezondheid & Focus</a>
                    <a href="./shop-per-doel.html">Topprestaties</a>
                </div>
            </div>
            <a href="./accessoires.html">Accessoires</a>
            <a href="./oefeningen.html">Oefeningen</a>
            <a href="./blog.html" class="active">Blog</a>
        </div>
    </div>

    <!-- Blog Post Content -->
    <div class="blog-post-container">
        <div class="blog-post-content">
            <!-- Breadcrumb -->
            <nav class="breadcrumb">
                <a href="./index.html">Home</a> → 
                <a href="./blog.html">Blog</a> → 
                <span id="breadcrumb-title">Artikel</span>
            </nav>

            <!-- Article Header -->
            <header class="article-header">
                <div class="article-category" id="article-category">Supplementen</div>
                <h1 id="article-title">Blog Artikel Titel</h1>
                <div class="article-meta">
                    <span class="article-date" id="article-date">15 januari 2025</span>
                    <span class="article-read-time" id="article-read-time">8 min leestijd</span>
                </div>
                <img id="article-image" src="./Afbeeldingen/blog-placeholder.jpg" alt="Artikel afbeelding" class="article-hero-image">
            </header>

            <!-- Article Content -->
            <article class="article-content" id="article-content">
                <div class="article-intro">
                    <p>Dit artikel wordt dynamisch geladen op basis van de URL parameter. In een echte implementatie zou dit content uit een CMS of database komen.</p>
                </div>

                <div class="article-body">
                    <h2>Artikel inhoud wordt hier geladen...</h2>
                    <p>De volledige inhoud van het blog artikel zou hier staan. Dit zou normaal gesproken uit een database of CMS komen.</p>
                    
                    <h3>Belangrijke punten:</h3>
                    <ul>
                        <li>Wetenschappelijk onderbouwde informatie</li>
                        <li>Praktische tips en adviezen</li>
                        <li>Onafhankelijke reviews en vergelijkingen</li>
                    </ul>

                    <h3>Conclusie</h3>
                    <p>Hier zou de conclusie van het artikel staan met de belangrijkste takeaways voor de lezer.</p>
                </div>
            </article>

            <!-- Related Articles -->
            <section class="related-articles">
                <h2>Gerelateerde Artikelen</h2>
                <div class="related-grid" id="related-grid">
                    <!-- Related articles will be populated by JavaScript -->
                </div>
            </section>

            <!-- Back to Blog -->
            <div class="back-to-blog">
                <a href="./blog.html" class="back-button">← Terug naar alle artikelen</a>
            </div>
        </div>

        <!-- Sidebar -->
        <aside class="blog-post-sidebar">
            <div class="sidebar-section">
                <h3>📧 Nieuwsbrief</h3>
                <p>Ontvang wekelijks nieuwe artikelen en reviews</p>
                <div class="newsletter-signup">
                    <input type="email" placeholder="Je e-mailadres" class="newsletter-input">
                    <button class="newsletter-button">Aanmelden</button>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>🔍 Gerelateerde Supplementen</h3>
                <div class="related-supplements">
                    <a href="./supplementen.html?category=Eiwitten" class="supplement-link">
                        <span class="supplement-icon">🥩</span>
                        Eiwitpoeders
                    </a>
                    <a href="./supplementen.html?category=Creatine" class="supplement-link">
                        <span class="supplement-icon">💪</span>
                        Creatine
                    </a>
                    <a href="./supplementen.html?category=Pre-workout" class="supplement-link">
                        <span class="supplement-icon">⚡</span>
                        Pre-workout
                    </a>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>🎯 Populaire Artikelen</h3>
                <div class="popular-articles" id="popular-articles">
                    <!-- Popular articles will be populated by JavaScript -->
                </div>
            </div>
        </aside>
    </div>

    <!-- Newsletter CTA Section -->
    <div class="newsletter-cta-section">
        <div class="newsletter-cta-content">
            <h2>Blijf op de hoogte van de nieuwste supplement inzichten</h2>
            <p>Ontvang wekelijks onafhankelijke reviews, wetenschappelijke updates en praktische tips direct in je inbox.</p>
            <div class="newsletter-form">
                <input type="email" placeholder="Jouw e-mailadres" class="newsletter-input" required>
                <button class="newsletter-button">Gratis Aanmelden</button>
            </div>
            <div class="newsletter-benefits">
                <span>✓ Geen spam</span>
                <span>✓ Uitschrijven altijd mogelijk</span>
                <span>✓ 100% gratis</span>
            </div>
        </div>
    </div>

    <script src="main.js"></script>
    <script>
        // Simple blog post loader based on URL parameter
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const slug = urlParams.get('slug');
            
            if (slug) {
                loadBlogPost(slug);
            } else {
                // Redirect to blog overview if no slug provided
                window.location.href = './blog.html';
            }
        });

        function loadBlogPost(slug) {
            // In a real implementation, this would fetch from an API or CMS
            // For now, we'll use the same sample data from main.js
            const samplePost = {
                title: "Blog Artikel - " + slug.replace(/-/g, ' '),
                category: "Supplementen",
                date: "15 januari 2025",
                readTime: "8 min",
                image: "./Afbeeldingen/blog-placeholder.jpg",
                content: "Dit is een voorbeeld artikel. In een echte implementatie zou hier de volledige content staan."
            };

            // Update page content
            document.getElementById('article-title').textContent = samplePost.title;
            document.getElementById('article-category').textContent = samplePost.category;
            document.getElementById('article-date').textContent = samplePost.date;
            document.getElementById('article-read-time').textContent = samplePost.readTime + ' leestijd';
            document.getElementById('article-image').src = samplePost.image;
            document.getElementById('breadcrumb-title').textContent = samplePost.title;
            
            // Update page title and meta
            document.title = samplePost.title + ' - SuppRadar';
            
            // Load related articles (simplified)
            loadRelatedArticles();
            loadPopularArticles();
        }

        function loadRelatedArticles() {
            const relatedGrid = document.getElementById('related-grid');
            relatedGrid.innerHTML = `
                <div class="related-article">
                    <img src="./Afbeeldingen/blog-placeholder.jpg" alt="Gerelateerd artikel">
                    <h4>Gerelateerd Artikel 1</h4>
                    <p>Korte beschrijving van het gerelateerde artikel.</p>
                    <a href="./blog-post.html?slug=gerelateerd-1">Lees meer →</a>
                </div>
                <div class="related-article">
                    <img src="./Afbeeldingen/blog-placeholder.jpg" alt="Gerelateerd artikel">
                    <h4>Gerelateerd Artikel 2</h4>
                    <p>Korte beschrijving van het gerelateerde artikel.</p>
                    <a href="./blog-post.html?slug=gerelateerd-2">Lees meer →</a>
                </div>
            `;
        }

        function loadPopularArticles() {
            const popularArticles = document.getElementById('popular-articles');
            popularArticles.innerHTML = `
                <div class="popular-article">
                    <a href="./blog-post.html?slug=populair-1">Populair Artikel 1</a>
                </div>
                <div class="popular-article">
                    <a href="./blog-post.html?slug=populair-2">Populair Artikel 2</a>
                </div>
                <div class="popular-article">
                    <a href="./blog-post.html?slug=populair-3">Populair Artikel 3</a>
                </div>
            `;
        }
    </script>
</body>
</html>
