<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="SuppRadar Blog - Wetenschappelijk onderbouwde artikelen over supplementen, voeding en training. Objectieve tips voor spieropbouw en slimme supplementkeuzes.">
    <meta name="keywords" content="supplement blog, fitness tips, voeding, training, spieropbouw, wetenschappelijk onderbouwd">
    <meta name="author" content="SuppRadar">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://suppradar.com/blog.html">
    <meta property="og:title" content="SuppRadar Blog - Tips & Informatie over Supplementen en Fitness">
    <meta property="og:description" content="Wetenschappelijk onderbouwde artikelen over supplementen, voeding en training. Objectieve tips voor spieropbouw en slimme supplementkeuzes.">
    <meta property="og:image" content="https://suppradar.com/Afbeeldingen/blog-hero.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://suppradar.com/blog.html">
    <meta property="twitter:title" content="SuppRadar Blog - Tips & Informatie over Supplementen en Fitness">
    <meta property="twitter:description" content="Wetenschappelijk onderbouwde artikelen over supplementen, voeding en training.">
    <meta property="twitter:image" content="https://suppradar.com/Afbeeldingen/blog-hero.jpg">

    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="main.css">
    <title>Blog: Tips & Informatie over Supplementen en Fitness - SuppRadar</title>
</head>
<body class="blog-page">
    <div class="header">
        <!-- Mobile hamburger menu -->
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="logo-container">
            <a href="./index.html" class="logo-link">
                <img src="./Afbeeldingen/logo.png" alt="SuppRadar Logo">
                <h1>SuppRadar</h1>
            </a>
        </div>
        <div class="nav-menu" id="nav-menu">
            <a href="./index.html">Home</a>
            <a href="./supplementen.html">Supplementen</a>
            <a href="./calorie-calculator.html">Calorie Calculator</a>
            <div class="dropdown">
                <a href="./shop-per-doel.html">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="./shop-per-doel.html">Gewichtsverlies</a>
                    <a href="./shop-per-doel.html">Spieropbouw</a>
                    <a href="./shop-per-doel.html">Uithoudingsvermogen</a>
                    <a href="./shop-per-doel.html">Fitness & Onderhoud</a>
                    <a href="./shop-per-doel.html">Gezondheid & Focus</a>
                    <a href="./shop-per-doel.html">Topprestaties</a>
                </div>
            </div>
            <a href="./accessoires.html">Accessoires</a>
            <a href="./oefeningen.html">Oefeningen</a>
            <a href="./blog.html" class="active">Blog</a>
        </div>
    </div>

    <!-- Hero Section -->
    <div class="blog-hero">
        <div class="hero-content">
            <h1>Blog: Tips & Informatie over Supplementen en Fitness</h1>
            <p class="hero-description">Op zoek naar eerlijke info over supplementen, voeding en training? In onze blog vind je wetenschappelijk onderbouwde artikelen, tips voor spieropbouw en slimme supplementkeuzes – objectief en no-nonsense.</p>
        </div>
    </div>

    <div class="blog-container">
        <!-- Sidebar with Filters -->
        <div class="blog-sidebar">
            <div class="sidebar-section">
                <h3>Zoeken</h3>
                <div class="search-container">
                    <input type="text" id="blog-search" placeholder="Zoek artikelen..." class="search-input">
                    <button class="search-button" onclick="searchBlogs()">🔍</button>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>Categorieën</h3>
                <div class="category-filters">
                    <label class="filter-checkbox">
                        <input type="checkbox" value="alle" checked onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Alle artikelen
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" value="supplementen" onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Supplementen
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" value="voeding" onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Voeding
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" value="training" onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Training
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" value="spieropbouw" onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Spieropbouw
                    </label>
                    <label class="filter-checkbox">
                        <input type="checkbox" value="gewichtsverlies" onchange="filterBlogs()">
                        <span class="checkmark"></span>
                        Gewichtsverlies
                    </label>
                </div>
            </div>

            <div class="sidebar-section">
                <h3>Sorteren</h3>
                <select id="sort-select" onchange="sortBlogs()" class="sort-dropdown">
                    <option value="newest">Nieuwste eerst</option>
                    <option value="oldest">Oudste eerst</option>
                    <option value="popular">Meest populair</option>
                    <option value="alphabetical">Alfabetisch</option>
                </select>
            </div>

            <!-- CTA Section in Sidebar -->
            <div class="sidebar-cta">
                <div class="cta-item">
                    <h4>🔍 Supplement Vergelijker</h4>
                    <p>Vergelijk de beste supplementen voor jouw doel</p>
                    <a href="./supplementen.html" class="cta-button">Bekijk Supplementen</a>
                </div>
                
                <div class="cta-item">
                    <h4>📧 Nieuwsbrief</h4>
                    <p>Ontvang wekelijks onafhankelijke reviews</p>
                    <a href="#newsletter" class="cta-button secondary">Meld Je Aan</a>
                </div>
                
                <div class="cta-item">
                    <h4>💪 Start Hier</h4>
                    <p>Supplementen voor spiergroei</p>
                    <a href="./supplementen.html?category=Eiwitten" class="cta-button">Spieropbouw Tips</a>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="blog-main">
            <div class="blog-controls">
                <div class="results-info">
                    <span id="results-count">12 artikelen gevonden</span>
                </div>
            </div>

            <!-- Blog Grid -->
            <div class="blog-grid" id="blog-grid">
                <!-- Blog articles will be populated by JavaScript -->
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <button class="pagination-btn" id="prev-btn" onclick="previousPage()" disabled>← Vorige</button>
                <span class="pagination-info">Pagina <span id="current-page">1</span> van <span id="total-pages">2</span></span>
                <button class="pagination-btn" id="next-btn" onclick="nextPage()">Volgende →</button>
            </div>
        </div>
    </div>

    <!-- Newsletter CTA Section -->
    <div class="newsletter-cta-section" id="newsletter">
        <div class="newsletter-cta-content">
            <h2>Blijf op de hoogte van de nieuwste supplement inzichten</h2>
            <p>Ontvang wekelijks onafhankelijke reviews, wetenschappelijke updates en praktische tips direct in je inbox.</p>
            <div class="newsletter-form">
                <input type="email" placeholder="Jouw e-mailadres" class="newsletter-input" required>
                <button class="newsletter-button">Gratis Aanmelden</button>
            </div>
            <div class="newsletter-benefits">
                <span>✓ Geen spam</span>
                <span>✓ Uitschrijven altijd mogelijk</span>
                <span>✓ 100% gratis</span>
            </div>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
